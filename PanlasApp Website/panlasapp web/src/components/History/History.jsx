import React, { useEffect, useState } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";

function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];
  const tags = meal.dietaryTags || [];
  return BADGE_MAP.filter(badge =>
    tags.some(tag => tag.toLowerCase() === badge.key.toLowerCase())
  );
}
// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const History = () => {
  const [recentMeals, setRecentMeals] = useState([]);
  const [mealsFromSavedPlans, setMealsFromSavedPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const token = localStorage.getItem("token");

  // Function to load recently viewed meals
  const loadRecentlyViewedMeals = async () => {
    if (!token) {
      console.log('No token available for loading recently viewed meals');
      return;
    }
    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const response = await axios.get("http://localhost:5000/api/users/recently-viewed-meals", {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Website API response:', JSON.stringify(response.data, null, 2));

      const meals = response.data.recentlyViewedMeals || [];
      console.log('Setting recent meals count:', meals.length);
      console.log('Recent meals:', JSON.stringify(meals.map(m => ({ name: m.name, id: m.id || m._id })), null, 2));

      setRecentMeals(meals);
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      console.error('Error details:', JSON.stringify(error.response?.data || error, null, 2));
      setRecentMeals([]);
    }
  };

  // Function to load meals from saved plans
  const loadMealsFromSavedPlans = async () => {
    if (!token) return;
    try {
      const response = await axios.get("http://localhost:5000/api/users/meals-from-saved-plans", {
        headers: { Authorization: `Bearer ${token}` }
      });
      setMealsFromSavedPlans(response.data.mealsFromSavedPlans || []);
    } catch (error) {
      console.error('Error loading meals from saved plans:', error);
      setMealsFromSavedPlans([]);
    }
  };

  // Function to load all history data
  const loadHistoryData = async () => {
    setLoading(true);
    await Promise.all([
      loadRecentlyViewedMeals(),
      loadMealsFromSavedPlans()
    ]);
    setLoading(false);
  };

  // Load data on mount
  useEffect(() => {
    loadHistoryData();
  }, [token]);

  // Add event listener for when user returns to this page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the data
        loadHistoryData();
      }
    };

    const handleFocus = () => {
      // Window gained focus, refresh the data
      loadHistoryData();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [token]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format meal type for display
  const formatMealType = (mealType) => {
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };

  return (
    <Layout>
      <div className="main-content">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1>Family Meal History</h1>
          <button
            onClick={loadHistoryData}
            disabled={loading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

          {/* Meals from Saved Plans Section */}
          <div className="history-section">
            <h2>Recently Added to Family Meal Plans</h2>
            {mealsFromSavedPlans.length === 0 ? (
              <p>No meals from saved plans yet.</p>
            ) : (
              <div className="food-grid">
                {mealsFromSavedPlans.map((meal, index) => (
<div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
  <div className="food-card-image">
    <img src={meal.image} alt={meal.name} />
  </div>
  <div className="food-card-content">
    {/* 1. Meal Name */}
    <h3 className="food-card-title">{meal.name}</h3>
    {/* 2. Meta row: Calories, Prep Time, Rating */}
    <div className="food-card-meta">
      {meal.calories && (
        <div className="meta-item calories-tag">
          <span>{meal.calories} cal</span>
        </div>
      )}
      {meal.prepTime && (
        <div className="meta-item prep-time-tag">
          <FaClock /> <span>{meal.prepTime} min</span>
        </div>
      )}
      <div className="meta-item rating">
        <span>{meal.rating} &#9733;</span>
      </div>
    </div>
    {/* 3. Dietary Tags */}
    {getDietaryBadges(meal).length > 0 && (
      <div className="food-card-tags">
        {getDietaryBadges(meal).map((badge, idx) => (
          <span
            key={idx}
            className="dietary-tag"
            style={{
              background: badge.color,
              color: '#fff',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.25em',
            }}
            title={badge.label}
          >
            <span className="dietary-tag-icon">{badge.icon}</span>
            <span className="dietary-tag-label">{badge.label}</span>
          </span>
        ))}
      </div>
    )}
    {/* 4. Description (optional, if you want) */}
    {meal.description && (
      <div className="food-card-description">
        <span>
          {meal.description.length > 80
            ? meal.description.slice(0, 80) + "..."
            : meal.description}
        </span>
      </div>
    )}
    {/* 5. Meal Plan Info */}
    <div className="meal-plan-info">
      <div className="meal-plan-details">
        <span className={`meal-type-badge meal-type-${meal.addedToMealType?.toLowerCase()}`}>
          {formatMealType(meal.addedToMealType)}
        </span>
        <span className="added-date">
          Added {formatDate(meal.addedAt)}
        </span>
      </div>
      <div className="planned-for">
        Planned for: {new Date(meal.addedToDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })}
      </div>
      {meal.planName && (
        <div className="plan-name" style={{
          fontSize: '0.85em',
          color: '#666',
          fontStyle: 'italic',
          marginTop: '4px'
        }}>
          From: {meal.planName}
        </div>
      )}
    </div>
  </div>
</div>
                ))}
              </div>
            )}
          </div>

          {/* Recently Viewed Meals Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Viewed Meals</h2>
              <button
                onClick={loadRecentlyViewedMeals}
                disabled={loading}
                style={{
                  padding: '5px 15px',
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  opacity: loading ? 0.6 : 1,
                  fontSize: '12px'
                }}
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>
            {loading ? (
              <p>Loading recently viewed meals...</p>
            ) : recentMeals.length === 0 ? (
              <p>No recently viewed meals yet. Try viewing some meals first!</p>
            ) : (
              <div className="food-grid">
                {recentMeals.map((meal) => (
                  <div key={meal.id || meal._id} className="food-card">
                    <div className="food-card-image">
                      <img src={meal.image} alt={meal.name} />
                    </div>
                    <div className="food-card-content">
                      <h3>{meal.name}</h3>
                      <div className="food-card-meta">
                        <div className="category-tag">
                          <span>{meal.category}</span>
                        </div>
                        <div className="rating">
                          <span>{meal.rating} &#9733;</span>
                        </div>
                      </div>
                      <div className="food-card-price">
                        <span className="price-range">
                          {getPesoSigns(meal.priceRange)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
      </div>
    </Layout>
  );
};

export default History;